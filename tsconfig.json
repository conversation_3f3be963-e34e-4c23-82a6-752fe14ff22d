{
  "compilerOptions": {
    "target": "ES2022",
    "lib": ["ES2022", "DOM", "DOM.Iterable"],
    "module": "nodenext",
    "moduleResolution": "nodenext",
    "allowImportingTsExtensions": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    /* Linting */
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noFallthroughCasesInSwitch": true,
    "allowJs": true,
    "checkJs": false,
    /* Interop */
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
    "forceConsistentCasingInFileNames": true,
    "skipLibCheck": true,
    /* Path mapping */
    "baseUrl": "./src",
    "typeRoots": ["./node_modules/@types", "./types"]
  },
  "include": ["src/**/*", "types/**/*"],
  "exclude": [
    "node_modules",
    "dist",
    "build",
    "coverage",
    "logs",
    "dev-dist",
    "public",
    "scripts",
    "api",
    "tests",
    "*.config.js",
    "*.config.ts",
    "*.config.mjs",
    "*.config.cjs",
    "eslint.config.js",
    "vite.config.js",
    "jest.config.js",
    "playwright.config.js",
    "webpack.config.js",
    "prettier.config.js",
    "babel.config.cjs"
  ]
}
