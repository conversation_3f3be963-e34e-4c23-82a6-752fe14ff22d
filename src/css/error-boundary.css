/**
 * @file ERROR BOUNDARY STYLES
 *
 * @version 1.0.0
 * <AUTHOR>
 * @contributors
 * @license MIT
 *
 * @description
 * CSS styles for error boundary components and fallback UI.
 * Provides consistent styling for error messages, recovery options,
 * and emergency calculator interface.
 */

/* ------------ ERROR BOUNDARY CONTAINER */

.error-boundary-container {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 10000;
    font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    backdrop-filter: blur(4px);
    animation: fadeIn 0.3s ease-out;
}

.error-boundary-container.show {
    display: flex;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        backdrop-filter: blur(0px);
    }
    to {
        opacity: 1;
        backdrop-filter: blur(4px);
    }
}

/* ------------ ERROR DIALOG */

.error-dialog {
    background: white;
    border-radius: 12px;
    padding: 30px;
    max-width: 500px;
    margin: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    text-align: center;
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from {
        transform: translateY(-20px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

.error-dialog h2 {
    color: #333;
    margin-bottom: 15px;
    font-size: 24px;
    font-weight: 600;
}

.error-dialog p {
    color: #666;
    margin-bottom: 25px;
    line-height: 1.5;
    font-size: 16px;
}

.error-icon {
    font-size: 48px;
    margin-bottom: 20px;
    display: block;
}

/* ------------ ERROR SEVERITY STYLES */

.error-severity-critical .error-icon::before {
    content: '🚨';
}

.error-severity-high .error-icon::before {
    content: '⚠️';
}

.error-severity-medium .error-icon::before {
    content: '⚡';
}

.error-severity-low .error-icon::before {
    content: 'ℹ️';
}

/* ------------ RECOVERY BUTTONS */

.recovery-options {
    margin-bottom: 20px;
}

.recovery-button {
    background: #007bff;
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 6px;
    cursor: pointer;
    margin: 5px;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.2s ease;
    min-width: 120px;
}

.recovery-button:hover {
    background: #0056b3;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 123, 255, 0.3);
}

.recovery-button:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(0, 123, 255, 0.3);
}

.recovery-button.secondary {
    background: #6c757d;
}

.recovery-button.secondary:hover {
    background: #545b62;
    box-shadow: 0 4px 8px rgba(108, 117, 125, 0.3);
}

.recovery-button.danger {
    background: #dc3545;
}

.recovery-button.danger:hover {
    background: #c82333;
    box-shadow: 0 4px 8px rgba(220, 53, 69, 0.3);
}

/* ------------ TECHNICAL DETAILS */

.technical-details {
    margin-top: 20px;
    text-align: left;
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.technical-details summary {
    cursor: pointer;
    font-weight: bold;
    color: #666;
    padding: 5px 0;
    user-select: none;
}

.technical-details summary:hover {
    color: #333;
}

.technical-details-content {
    margin-top: 10px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 12px;
    color: #666;
    line-height: 1.4;
}

.technical-details-content div {
    margin-bottom: 5px;
}

.technical-details-content strong {
    color: #333;
    font-weight: 600;
}

/* ------------ FALLBACK COMPONENTS */

.fallback-calculator-display,
.fallback-calculator-buttons,
.fallback-calculator-history {
    background: #f8f9fa;
    border: 2px solid #dee2e6;
    border-radius: 8px;
    padding: 20px;
    margin: 10px;
    text-align: center;
    font-family: system-ui, -apple-system, sans-serif;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.fallback-warning {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    color: #856404;
    padding: 10px;
    border-radius: 4px;
    margin-bottom: 15px;
    font-size: 14px;
    font-weight: 500;
}

.fallback-display {
    background: #ffffff;
    border: 1px solid #ced4da;
    border-radius: 4px;
    padding: 15px;
    font-size: 24px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    text-align: right;
    margin-bottom: 15px;
    min-height: 50px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
}

.fallback-button {
    background: #007bff;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 12px 16px;
    margin: 2px;
    cursor: pointer;
    font-size: 16px;
    min-width: 50px;
    transition: background-color 0.2s ease;
    font-weight: 500;
}

.fallback-button:hover {
    background: #0056b3;
}

.fallback-button:active {
    background: #004085;
}

.fallback-button.secondary {
    background: #6c757d;
}

.fallback-button.secondary:hover {
    background: #545b62;
}

/* ------------ EMERGENCY CALCULATOR */

.emergency-calculator {
    max-width: 400px;
    margin: 20px auto;
    padding: 20px;
    background: #f8f9fa;
    border: 2px solid #dc3545;
    border-radius: 12px;
    text-align: center;
    font-family: system-ui, -apple-system, sans-serif;
    box-shadow: 0 4px 12px rgba(220, 53, 69, 0.2);
}

.emergency-header {
    background: #f8d7da;
    color: #721c24;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 20px;
    border: 1px solid #f5c6cb;
}

.emergency-header h2 {
    margin: 0 0 10px 0;
    font-size: 20px;
    font-weight: 600;
}

.emergency-header p {
    margin: 0;
    font-size: 14px;
    line-height: 1.4;
}

.emergency-recovery {
    margin-top: 20px;
    padding: 15px;
    background: #d1ecf1;
    border-radius: 8px;
    border: 1px solid #bee5eb;
}

.emergency-recovery h4 {
    margin: 0 0 10px 0;
    color: #0c5460;
    font-size: 16px;
    font-weight: 600;
}

/* ------------ ERROR MESSAGES */

.fallback-error-message {
    padding: 15px;
    border-radius: 8px;
    margin: 10px;
    text-align: center;
    font-weight: 500;
    animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
    from {
        transform: translateY(-10px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

.fallback-error-message.severity-low {
    background: #d4edda;
    border: 1px solid #c3e6cb;
    color: #155724;
}

.fallback-error-message.severity-medium {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    color: #856404;
}

.fallback-error-message.severity-high {
    background: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
}

.fallback-error-message.severity-critical {
    background: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
    border-width: 2px;
    box-shadow: 0 2px 8px rgba(220, 53, 69, 0.2);
}

/* ------------ RESPONSIVE DESIGN */

@media (max-width: 768px) {
    .error-dialog {
        margin: 10px;
        padding: 20px;
        max-width: calc(100vw - 20px);
    }
    
    .error-dialog h2 {
        font-size: 20px;
    }
    
    .recovery-button {
        display: block;
        width: 100%;
        margin: 5px 0;
    }
    
    .emergency-calculator {
        margin: 10px;
        max-width: calc(100vw - 20px);
    }
    
    .fallback-calculator-display,
    .fallback-calculator-buttons,
    .fallback-calculator-history {
        margin: 5px;
        padding: 15px;
    }
}

@media (max-width: 480px) {
    .error-dialog {
        padding: 15px;
    }
    
    .error-icon {
        font-size: 36px;
        margin-bottom: 15px;
    }
    
    .fallback-display {
        font-size: 20px;
        padding: 12px;
    }
    
    .fallback-button {
        padding: 10px 12px;
        font-size: 14px;
        min-width: 45px;
    }
}

/* ------------ ACCESSIBILITY */

@media (prefers-reduced-motion: reduce) {
    .error-boundary-container,
    .error-dialog,
    .fallback-error-message {
        animation: none;
    }
    
    .recovery-button:hover {
        transform: none;
    }
}

@media (prefers-color-scheme: dark) {
    .error-dialog {
        background: #2d3748;
        color: #e2e8f0;
    }
    
    .error-dialog h2 {
        color: #f7fafc;
    }
    
    .error-dialog p {
        color: #cbd5e0;
    }
    
    .technical-details {
        background: #4a5568;
        border-color: #718096;
    }
    
    .technical-details summary {
        color: #cbd5e0;
    }
    
    .technical-details summary:hover {
        color: #f7fafc;
    }
    
    .fallback-calculator-display,
    .fallback-calculator-buttons,
    .fallback-calculator-history {
        background: #4a5568;
        border-color: #718096;
        color: #e2e8f0;
    }
    
    .fallback-display {
        background: #2d3748;
        border-color: #4a5568;
        color: #f7fafc;
    }
}
