{"name": "thegreatcalculator", "version": "2.0.0", "description": "A modern advanced scientific calculator with Apple-inspired design and responsive interface", "keywords": ["calculator", "scientific-calculator", "web-app", "responsive-design", "pwa", "mathematics", "tools"], "private": false, "sideEffects": false, "homepage": "https://thegreatcalculator.vercel.app", "bugs": {"url": "https://github.com/JotaRYT/TheGreatCalculator/issues"}, "repository": {"type": "git", "url": "git+https://github.com/JotaRYT/TheGreatCalculator.git"}, "license": "MIT", "author": {"name": "BleckWolf25", "url": "https://github.com/BleckWolf25"}, "type": "module", "main": "src/main.js", "directories": {"test": "tests"}, "scripts": {"dev": "vite", "build": "vite build", "build:analyze": "vite build --mode production && npm run analyze", "build:optimized": "npm run optimize && vite build --mode production", "preview": "vite preview", "start": "vite", "test": "NODE_OPTIONS='--experimental-vm-modules' jest --config jest.config.js", "test:watch": "NODE_OPTIONS='--experimental-vm-modules' jest --config jest.config.js --watch", "test:coverage": "NODE_OPTIONS='--experimental-vm-modules' jest --config jest.config.js --coverage", "test:core": "NODE_OPTIONS='--experimental-vm-modules' jest --config jest.config.js tests/core", "test:ui": "NODE_OPTIONS='--experimental-vm-modules' jest --config jest.config.js tests/ui", "test:api": "NODE_OPTIONS='--experimental-vm-modules' jest --config jest.config.js tests/api", "test:storage": "NODE_OPTIONS='--experimental-vm-modules' jest --config jest.config.js tests/storage", "test:export": "NODE_OPTIONS='--experimental-vm-modules' jest --config jest.config.js tests/export", "test:integration": "NODE_OPTIONS='--experimental-vm-modules' jest --config jest.config.js tests/integration", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:e2e:headed": "playwright test --headed", "test:accessibility": "NODE_OPTIONS='--experimental-vm-modules' jest --config jest.config.js tests/accessibility", "test:performance": "NODE_OPTIONS='--experimental-vm-modules' jest --config jest.config.js tests/performance", "test:all": "npm run test:coverage && npm run test:e2e", "lint": "eslint src/**/*.js", "lint:fix": "eslint src/**/*.js --fix", "format": "prettier --write \"src/**/*.{js,css,html}\" \"*.html\"", "build:clean": "rm -rf dist && npm run build", "build:pwa": "vite build --mode production", "preview:pwa": "vite preview --mode production", "generate:icons": "node scripts/generate-icons.js", "pwa:validate": "echo 'PWA Validation: Check Chrome DevTools > Lighthouse > Progressive Web App'", "optimize": "node scripts/optimize-bundle.js", "analyze": "npm run build && npx vite-bundle-analyzer dist", "size-check": "npm run build && npx bundlesize", "deploy": "node scripts/deploy-vercel.js", "deploy:preview": "vercel", "deploy:production": "vercel --prod", "vercel:build": "npm run build", "vercel:dev": "vite --port 3000", "health:check": "curl -f http://localhost:3000/api/health || echo 'Health check failed'", "performance:audit": "echo 'Performance audit: Use Lighthouse or WebPageTest'", "accessibility:audit": "echo 'Accessibility audit: Use axe-core or WAVE'", "semantic-release": "semantic-release"}, "dependencies": {"idb": "^8.0.3", "jspdf": "^3.0.1", "web-vitals": "^5.0.2"}, "devDependencies": {"@axe-core/playwright": "^4.10.2", "@babel/core": "^7.27.4", "@babel/plugin-transform-modules-commonjs": "^7.27.1", "@babel/preset-env": "^7.27.2", "@eslint/css": "^0.8.1", "@eslint/js": "^9.28.0", "@eslint/json": "^0.12.0", "@playwright/test": "^1.52.0", "@semantic-release/changelog": "^6.0.3", "@semantic-release/commit-analyzer": "^13.0.1", "@semantic-release/git": "^10.0.1", "@semantic-release/release-notes-generator": "^14.0.3", "@typescript-eslint/eslint-plugin": "^8.33.1", "@typescript-eslint/parser": "^8.33.1", "@vitejs/plugin-legacy": "^6.1.1", "axe-core": "^4.10.3", "babel-jest": "^29.7.0", "conventional-changelog-conventionalcommits": "^9.0.0", "core-js": "^3.43.0", "eslint": "^9.28.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-css": "^0.11.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jest": "^28.13.0", "eslint-plugin-promise": "^7.2.1", "eslint-plugin-security": "^3.0.1", "eslint-plugin-sonarjs": "^3.0.2", "eslint-plugin-unicorn": "^59.0.1", "globals": "^16.2.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "jest-html-reporters": "^3.1.7", "jest-performance-testing": "^1.0.0", "jest-watch-typeahead": "^2.2.2", "jsdom": "^26.0.1", "playwright": "^1.52.0", "prettier": "^3.5.3", "semantic-release": "^24.2.5", "sharp": "^0.34.2", "typescript": "^5.8.3", "vite": "^6.3.5", "vite-plugin-pwa": "^1.0.0", "workbox-cacheable-response": "^7.3.0", "workbox-expiration": "^7.3.0", "workbox-precaching": "^7.3.0", "workbox-routing": "^7.3.0", "workbox-strategies": "^7.3.0", "workbox-window": "^7.3.0"}, "engines": {"node": ">=22.0.0 <23.0.0", "npm": ">=11.0.0"}, "browserslist": [">0.2%", "not dead", "not ie <= 11", "not op_mini all"]}