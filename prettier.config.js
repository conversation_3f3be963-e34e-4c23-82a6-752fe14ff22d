/**
 * @file PRETTIER.CONFIG.JS
 * 
 * @version 1.0.0
 * <AUTHOR>
 * @contributors
 * @license MIT
 * 
 * @description
 * Prettier configuration for The Great Calculator project.
 * Ensures consistent code formatting and style.
 * Compatible with ESLint & editorconfig.
 */

// ------------ CONFIGURATION
export default {
  // Use single quotes
  singleQuote: true,
  
  // Trailing commas
  trailingComma: 'es5',

  // Use semi-colons
  semi: true,
  
  // Print width
  printWidth: 80,

  // Arrow function parens
  arrowParens: 'always',
  
  // Tab width
  tabWidth: 2,
  
  // End of line
  endOfLine: 'lf'
};
