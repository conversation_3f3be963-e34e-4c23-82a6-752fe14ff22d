/**
 * @file WE<PERSON><PERSON><PERSON>K CONFIGURATION FOR BUN<PERSON>LE OPTIMIZATION
 *
 * @version 1.0.0
 * <AUTHOR>
 * @contributors
 * @license MIT
 *
 * @description
 * Advanced Webpack configuration for The Great Calculator with comprehensive
 * bundle optimization strategies including code splitting, tree shaking,
 * and performance optimizations.
 */

// ------------ <PERSON>MP<PERSON><PERSON>
import { join, resolve as _resolve } from 'path';
import HtmlWebpackPlugin from 'html-webpack-plugin';
import MiniCssExtractPlugin, { loader as _loader } from 'mini-css-extract-plugin';
import TerserPlugin from 'terser-webpack-plugin';
import CssMinimizerPlugin from 'css-minimizer-webpack-plugin';
import { BundleAnalyzerPlugin } from 'webpack-bundle-analyzer';
import CompressionPlugin from 'compression-webpack-plugin';

// ------------ ENVIRONMENT CONFIGURATION
const isDevelopment = process.env.NODE_ENV !== 'production';
const isProduction = !isDevelopment;

// ------------ CONFIGURATION
export const entry = {
  // Main application entry
  main: './src/js/main.js',

  // Core calculator functionality (critical path)
  core: {
    import: [
      './src/js/modules/core/state.js',
      './src/js/modules/core/operations.js',
      './src/js/modules/ui/display.js'
    ],
    dependOn: 'shared'
  },

  // API and calculation modules
  api: {
    import: './src/js/modules/api/calculatorAPI.js',
    dependOn: ['shared', 'core']
  },

  // Shared dependencies
  shared: 'lodash' // Add any shared dependencies here
};
export const mode = isProduction ? 'production' : 'development';
export const devServer = {
  contentBase: join(__dirname, 'dist'),
  compress: true,
  port: 3000,
  hot: true,
  open: true
};
export const output = {
  path: _resolve(__dirname, 'dist'),
  filename: isProduction
    ? 'js/[name].[contenthash:8].js'
    : 'js/[name].js',
  chunkFilename: isProduction
    ? 'js/[name].[contenthash:8].chunk.js'
    : 'js/[name].chunk.js',
  clean: true,
  publicPath: '/'
};
export const resolve = {
  extensions: ['.js', '.json'],
  alias: {
    '@': _resolve(__dirname, 'src'),
    '@modules': _resolve(__dirname, 'src/js/modules'),
    '@core': _resolve(__dirname, 'src/js/modules/core'),
    '@ui': _resolve(__dirname, 'src/js/modules/ui'),
    '@api': _resolve(__dirname, 'src/js/modules/api')
  }
};
export const module = {
  rules: [
    // JavaScript files
    {
      test: /\.js$/,
      exclude: /node_modules/,
      use: {
        loader: 'babel-loader',
        options: {
          presets: [
            ['@babel/preset-env', {
              targets: {
                browsers: ['> 1%', 'last 2 versions']
              },
              modules: false, // Keep ES modules for tree shaking
              useBuiltIns: 'usage',
              corejs: 3
            }]
          ],
          plugins: [
            '@babel/plugin-syntax-dynamic-import',
            '@babel/plugin-proposal-class-properties'
          ]
        }
      }
    },

    // CSS files
    {
      test: /\.css$/,
      use: [
        isProduction ? _loader : 'style-loader',
        {
          loader: 'css-loader',
          options: {
            importLoaders: 1,
            sourceMap: isDevelopment
          }
        },
        {
          loader: 'postcss-loader',
          options: {
            postcssOptions: {
              plugins: [
                ['autoprefixer'],
                ...(isProduction ? [['cssnano']] : [])
              ]
            }
          }
        }
      ]
    },

    // Asset files
    {
      test: /\.(png|jpe?g|gif|svg|ico)$/,
      type: 'asset/resource',
      generator: {
        filename: 'images/[name].[hash:8][ext]'
      }
    },

    // Font files
    {
      test: /\.(woff|woff2|eot|ttf|otf)$/,
      type: 'asset/resource',
      generator: {
        filename: 'fonts/[name].[hash:8][ext]'
      }
    }
  ]
};
export const optimization = {
  minimize: isProduction,
  minimizer: [
    // JavaScript minification
    new TerserPlugin({
      terserOptions: {
        compress: {
          drop_console: isProduction,
          drop_debugger: isProduction,
          pure_funcs: isProduction ? [
            'console.log',
            'console.debug',
            'console.info',
            'console.warn'
          ] : [],
          passes: 2
        },
        mangle: {
          safari10: true
        },
        format: {
          comments: false
        }
      },
      extractComments: false
    }),

    // CSS minification
    new CssMinimizerPlugin()
  ],

  // Advanced code splitting
  splitChunks: {
    chunks: 'all',
    minSize: 20000,
    maxSize: 250000,
    cacheGroups: {
      // Vendor libraries
      vendor: {
        test: /[\\/]node_modules[\\/]/,
        name: 'vendors',
        chunks: 'all',
        priority: 10
      },

      // Core calculator modules
      core: {
        test: /[\\/]src[\\/]js[\\/]modules[\\/]core[\\/]/,
        name: 'core',
        chunks: 'all',
        priority: 8
      },

      // UI modules
      ui: {
        test: /[\\/]src[\\/]js[\\/]modules[\\/]ui[\\/]/,
        name: 'ui',
        chunks: 'all',
        priority: 7
      },

      // API modules
      api: {
        test: /[\\/]src[\\/]js[\\/]modules[\\/]api[\\/]/,
        name: 'api',
        chunks: 'all',
        priority: 6
      },

      // Storage modules
      storage: {
        test: /[\\/]src[\\/]js[\\/]modules[\\/]storage[\\/]/,
        name: 'storage',
        chunks: 'all',
        priority: 5
      },

      // Export modules (lazy loaded)
      export: {
        test: /[\\/]src[\\/]js[\\/]modules[\\/]export[\\/]/,
        name: 'export',
        chunks: 'async',
        priority: 4
      },

      // Error handling modules (lazy loaded)
      error: {
        test: /[\\/]src[\\/]js[\\/]modules[\\/]error[\\/]/,
        name: 'error-handling',
        chunks: 'async',
        priority: 3
      },

      // Performance modules (lazy loaded)
      performance: {
        test: /[\\/]src[\\/]js[\\/]modules[\\/]performance[\\/]/,
        name: 'performance',
        chunks: 'async',
        priority: 2
      },

      // Default chunk for remaining modules
      default: {
        minChunks: 2,
        priority: 1,
        reuseExistingChunk: true
      }
    }
  },

  // Runtime chunk optimization
  runtimeChunk: {
    name: 'runtime'
  },

  // Module concatenation for better tree shaking
  concatenateModules: isProduction,

  // Side effects configuration for tree shaking
  sideEffects: false,

  // Use deterministic module ids for better caching
  moduleIds: 'deterministic',
  chunkIds: 'deterministic'
};
export const plugins = [
  // HTML generation
  new HtmlWebpackPlugin({
    template: './index.html',
    filename: 'index.html',
    inject: 'body',
    minify: isProduction ? {
      removeComments: true,
      collapseWhitespace: true,
      removeRedundantAttributes: true,
      useShortDoctype: true,
      removeEmptyAttributes: true,
      removeStyleLinkTypeAttributes: true,
      keepClosingSlash: true,
      minifyJS: true,
      minifyCSS: true,
      minifyURLs: true
    } : false
  }),

  // CSS extraction for production
  ...(isProduction ? [
    new MiniCssExtractPlugin({
      filename: 'css/[name].[contenthash:8].css',
      chunkFilename: 'css/[name].[contenthash:8].chunk.css'
    })
  ] : []),

  // Compression for production
  ...(isProduction ? [
    new CompressionPlugin({
      algorithm: 'gzip',
      test: /\.(js|css|html|svg)$/,
      threshold: 8192,
      minRatio: 0.8
    })
  ] : []),

  // Bundle analyzer (optional)
  ...(process.env.ANALYZE ? [
    new BundleAnalyzerPlugin({
      analyzerMode: 'static',
      openAnalyzer: false,
      reportFilename: 'bundle-report.html'
    })
  ] : [])
];
export const devtool = isDevelopment ? 'eval-source-map' : 'source-map';
export const performance = {
  hints: isProduction ? 'warning' : false,
  maxEntrypointSize: 250000,
  maxAssetSize: 250000
};
export const stats = {
  colors: true,
  modules: false,
  children: false,
  chunks: false,
  chunkModules: false
};
